// 全局变量
let events = [];
let participants = [];
let currentEventId = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadSampleData();
    updateDashboard();
});

// 初始化应用
function initializeApp() {
    // 侧边栏导航事件
    const sidebarLinks = document.querySelectorAll('.sidebar .nav-link');
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            if (section) {
                showSection(section);
                updateActiveNavLink(this);
            }
        });
    });

    // 搜索功能
    const searchInput = document.getElementById('searchEvents');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterEvents(this.value);
        });
    }
}

// 显示指定的内容区域
function showSection(sectionId) {
    // 隐藏所有内容区域
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });

    // 显示指定区域
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
    }

    // 根据不同区域加载相应数据
    switch(sectionId) {
        case 'dashboard':
            updateDashboard();
            break;
        case 'registration':
            loadEventsTable();
            break;
        case 'enrollment':
            loadEnrollmentData();
            break;
        case 'tracking':
            loadTrackingData();
            break;
        case 'archive':
            loadArchiveData();
            break;
    }
}

// 更新活动导航链接
function updateActiveNavLink(activeLink) {
    const sidebarLinks = document.querySelectorAll('.sidebar .nav-link');
    sidebarLinks.forEach(link => {
        link.classList.remove('active');
    });
    activeLink.classList.add('active');
}

// 加载示例数据
function loadSampleData() {
    events = [
        {
            id: 'EVT001',
            name: '2024年春季马拉松',
            type: '体育竞技',
            status: 'active',
            startDate: '2024-04-15',
            endDate: '2024-04-15',
            maxParticipants: 500,
            currentParticipants: 342,
            createTime: '2024-03-01',
            description: '春季马拉松比赛，全程42.195公里'
        },
        {
            id: 'EVT002',
            name: '编程挑战赛',
            type: '技能竞赛',
            status: 'pending',
            startDate: '2024-05-20',
            endDate: '2024-05-22',
            maxParticipants: 200,
            currentParticipants: 89,
            createTime: '2024-03-15',
            description: '为期三天的编程挑战赛'
        },
        {
            id: 'EVT003',
            name: '摄影大赛',
            type: '艺术创作',
            status: 'completed',
            startDate: '2024-02-01',
            endDate: '2024-02-28',
            maxParticipants: 100,
            currentParticipants: 76,
            createTime: '2024-01-10',
            description: '主题摄影作品征集大赛'
        }
    ];

    participants = [
        {
            id: 'P001',
            name: '张三',
            email: '<EMAIL>',
            phone: '13800138001',
            eventId: 'EVT001',
            registrationTime: '2024-03-05',
            status: 'confirmed'
        },
        {
            id: 'P002',
            name: '李四',
            email: '<EMAIL>',
            phone: '13800138002',
            eventId: 'EVT001',
            registrationTime: '2024-03-06',
            status: 'confirmed'
        }
    ];
}

// 更新仪表盘数据
function updateDashboard() {
    const totalEvents = events.length;
    const activeEvents = events.filter(e => e.status === 'active').length;
    const pendingEvents = events.filter(e => e.status === 'pending').length;
    const totalParticipants = events.reduce((sum, event) => sum + event.currentParticipants, 0);

    // 更新统计卡片
    document.getElementById('totalEvents').textContent = totalEvents;
    document.getElementById('activeEvents').textContent = activeEvents;
    document.getElementById('pendingEvents').textContent = pendingEvents;
    document.getElementById('totalParticipants').textContent = totalParticipants;

    // 更新最近活动表格
    updateRecentEventsTable();
}

// 更新最近活动表格
function updateRecentEventsTable() {
    const tableBody = document.getElementById('recentEventsTable');
    if (!tableBody) return;

    if (events.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }

    const recentEvents = events.slice(0, 5); // 显示最近5个赛事
    const html = recentEvents.map(event => `
        <tr>
            <td>${event.name}</td>
            <td><span class="badge status-${event.status}">${getStatusText(event.status)}</span></td>
            <td>${event.currentParticipants}/${event.maxParticipants}</td>
            <td>${formatDate(event.startDate)}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewEvent('${event.id}')">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="editEvent('${event.id}')">
                    <i class="fas fa-edit"></i>
                </button>
            </td>
        </tr>
    `).join('');

    tableBody.innerHTML = html;
}

// 加载赛事表格
function loadEventsTable() {
    const tableBody = document.getElementById('eventsTable');
    if (!tableBody) return;

    if (events.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无赛事数据</td></tr>';
        return;
    }

    const html = events.map(event => `
        <tr>
            <td>${event.id}</td>
            <td>${event.name}</td>
            <td>${event.type}</td>
            <td><span class="badge status-${event.status}">${getStatusText(event.status)}</span></td>
            <td>${formatDate(event.createTime)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewEvent('${event.id}')" title="查看">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-secondary" onclick="editEvent('${event.id}')" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteEvent('${event.id}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');

    tableBody.innerHTML = html;
}

// 过滤赛事
function filterEvents(searchTerm) {
    const filteredEvents = events.filter(event => 
        event.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.id.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const tableBody = document.getElementById('eventsTable');
    if (!tableBody) return;

    if (filteredEvents.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">未找到匹配的赛事</td></tr>';
        return;
    }

    const html = filteredEvents.map(event => `
        <tr>
            <td>${event.id}</td>
            <td>${event.name}</td>
            <td>${event.type}</td>
            <td><span class="badge status-${event.status}">${getStatusText(event.status)}</span></td>
            <td>${formatDate(event.createTime)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewEvent('${event.id}')" title="查看">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-secondary" onclick="editEvent('${event.id}')" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteEvent('${event.id}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');

    tableBody.innerHTML = html;
}

// 工具函数
function getStatusText(status) {
    const statusMap = {
        'pending': '待审核',
        'active': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
    };
    return statusMap[status] || status;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

// 赛事操作函数
function viewEvent(eventId) {
    const event = events.find(e => e.id === eventId);
    if (event) {
        alert(`查看赛事: ${event.name}\n状态: ${getStatusText(event.status)}\n参赛人数: ${event.currentParticipants}/${event.maxParticipants}`);
    }
}

function editEvent(eventId) {
    const event = events.find(e => e.id === eventId);
    if (event) {
        alert(`编辑赛事功能开发中: ${event.name}`);
    }
}

function deleteEvent(eventId) {
    if (confirm('确定要删除这个赛事吗？')) {
        events = events.filter(e => e.id !== eventId);
        loadEventsTable();
        updateDashboard();
        showNotification('赛事已删除', 'success');
    }
}

function showRegistrationForm() {
    const modal = new bootstrap.Modal(document.getElementById('newEventModal'));
    modal.show();
}

// 保存新赛事
function saveNewEvent() {
    const form = document.getElementById('newEventForm');
    const formData = new FormData(form);

    // 验证表单
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // 生成新的赛事ID
    const newEventId = 'EVT' + String(events.length + 1).padStart(3, '0');

    // 创建新赛事对象
    const newEvent = {
        id: newEventId,
        name: document.getElementById('eventName').value,
        type: document.getElementById('eventType').value,
        status: 'pending',
        startDate: document.getElementById('startDate').value.split('T')[0],
        endDate: document.getElementById('endDate').value.split('T')[0],
        maxParticipants: parseInt(document.getElementById('maxParticipants').value),
        currentParticipants: 0,
        createTime: new Date().toISOString().split('T')[0],
        description: document.getElementById('eventDescription').value,
        location: document.getElementById('eventLocation').value,
        contactPerson: document.getElementById('contactPerson').value,
        contactPhone: document.getElementById('contactPhone').value,
        registrationDeadline: document.getElementById('registrationDeadline').value
    };

    // 添加到赛事列表
    events.push(newEvent);

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('newEventModal'));
    modal.hide();

    // 清空表单
    form.reset();

    // 更新界面
    loadEventsTable();
    updateDashboard();
    showNotification('赛事创建成功！', 'success');
}

// 显示通知
function showNotification(message, type = 'info') {
    const notificationsContainer = document.getElementById('notifications');
    if (!notificationsContainer) return;

    const alertClass = `alert-${type}`;
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-sm alert-dismissible fade show`;
    notification.innerHTML = `
        <i class="fas fa-info-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    notificationsContainer.appendChild(notification);

    // 自动移除通知
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// 报名相关函数
function loadEnrollmentData() {
    loadAvailableEvents();
    loadParticipantsTable();
    updateEnrollmentStats();
    loadEnrollmentEventFilter();
}

function loadAvailableEvents() {
    const container = document.getElementById('availableEvents');
    if (!container) return;

    const availableEvents = events.filter(event =>
        event.status === 'active' || event.status === 'pending'
    );

    if (availableEvents.length === 0) {
        container.innerHTML = '<div class="col-12"><p class="text-muted text-center">暂无可报名赛事</p></div>';
        return;
    }

    const html = availableEvents.map(event => `
        <div class="col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">${event.name}</h6>
                    <p class="card-text text-muted small">${event.description || '暂无描述'}</p>
                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>${formatDate(event.startDate)}
                            <i class="fas fa-users ms-2 me-1"></i>${event.currentParticipants}/${event.maxParticipants}
                        </small>
                    </div>
                    <div class="progress mb-2" style="height: 6px;">
                        <div class="progress-bar" style="width: ${(event.currentParticipants / event.maxParticipants) * 100}%"></div>
                    </div>
                    <button class="btn btn-primary btn-sm" onclick="showEnrollmentForm('${event.id}')">
                        <i class="fas fa-user-plus me-1"></i>立即报名
                    </button>
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = html;
}

function showEnrollmentForm(eventId) {
    document.getElementById('enrollEventId').value = eventId;
    const modal = new bootstrap.Modal(document.getElementById('enrollmentModal'));
    modal.show();
}

function saveEnrollment() {
    const form = document.getElementById('enrollmentForm');

    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const eventId = document.getElementById('enrollEventId').value;
    const event = events.find(e => e.id === eventId);

    if (!event) {
        showNotification('赛事不存在', 'error');
        return;
    }

    if (event.currentParticipants >= event.maxParticipants) {
        showNotification('报名人数已满', 'warning');
        return;
    }

    // 生成新的参赛者ID
    const newParticipantId = 'P' + String(participants.length + 1).padStart(3, '0');

    const newParticipant = {
        id: newParticipantId,
        name: document.getElementById('participantName').value,
        email: document.getElementById('participantEmail').value,
        phone: document.getElementById('participantPhone').value,
        age: document.getElementById('participantAge').value,
        gender: document.getElementById('participantGender').value,
        notes: document.getElementById('participantNotes').value,
        eventId: eventId,
        registrationTime: new Date().toISOString().split('T')[0],
        status: 'pending'
    };

    participants.push(newParticipant);
    event.currentParticipants++;

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('enrollmentModal'));
    modal.hide();

    // 清空表单
    form.reset();

    // 更新界面
    loadEnrollmentData();
    updateDashboard();
    showNotification('报名成功！', 'success');
}

function loadParticipantsTable() {
    const tableBody = document.getElementById('participantsTable');
    if (!tableBody) return;

    if (participants.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无报名数据</td></tr>';
        return;
    }

    const html = participants.map(participant => {
        const event = events.find(e => e.id === participant.eventId);
        return `
            <tr>
                <td>${participant.name}</td>
                <td>${event ? event.name : '未知赛事'}</td>
                <td>
                    <div>${participant.email}</div>
                    <small class="text-muted">${participant.phone}</small>
                </td>
                <td>${formatDate(participant.registrationTime)}</td>
                <td><span class="badge status-${participant.status}">${getStatusText(participant.status)}</span></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-success" onclick="approveParticipant('${participant.id}')" title="批准">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="rejectParticipant('${participant.id}')" title="拒绝">
                            <i class="fas fa-times"></i>
                        </button>
                        <button class="btn btn-outline-primary" onclick="viewParticipant('${participant.id}')" title="查看">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');

    tableBody.innerHTML = html;
}

function updateEnrollmentStats() {
    const totalEnrollments = participants.length;
    const pendingEnrollments = participants.filter(p => p.status === 'pending').length;
    const confirmedEnrollments = participants.filter(p => p.status === 'confirmed').length;

    document.getElementById('totalEnrollments').textContent = totalEnrollments;
    document.getElementById('pendingEnrollments').textContent = pendingEnrollments;
    document.getElementById('confirmedEnrollments').textContent = confirmedEnrollments;
}

function loadEnrollmentEventFilter() {
    const select = document.getElementById('enrollmentEventFilter');
    if (!select) return;

    const options = events.map(event =>
        `<option value="${event.id}">${event.name}</option>`
    ).join('');

    select.innerHTML = '<option value="">所有赛事</option>' + options;
}

function approveParticipant(participantId) {
    const participant = participants.find(p => p.id === participantId);
    if (participant) {
        participant.status = 'confirmed';
        loadParticipantsTable();
        updateEnrollmentStats();
        showNotification('参赛者已批准', 'success');
    }
}

function rejectParticipant(participantId) {
    if (confirm('确定要拒绝这个参赛者吗？')) {
        const participant = participants.find(p => p.id === participantId);
        if (participant) {
            participant.status = 'rejected';
            loadParticipantsTable();
            updateEnrollmentStats();
            showNotification('参赛者已拒绝', 'warning');
        }
    }
}

function viewParticipant(participantId) {
    const participant = participants.find(p => p.id === participantId);
    if (participant) {
        const event = events.find(e => e.id === participant.eventId);
        alert(`参赛者信息:\n姓名: ${participant.name}\n邮箱: ${participant.email}\n手机: ${participant.phone}\n赛事: ${event ? event.name : '未知'}\n状态: ${getStatusText(participant.status)}`);
    }
}

function refreshEnrollmentData() {
    loadEnrollmentData();
    showNotification('数据已刷新', 'info');
}

// 跟踪相关函数
function loadTrackingData() {
    updateTrackingStats();
    loadTrackingTable();
}

function updateTrackingStats() {
    const activeEventsCount = events.filter(e => e.status === 'active').length;
    const upcomingEventsCount = events.filter(e => e.status === 'pending').length;
    const activeParticipants = participants.filter(p => p.status === 'confirmed').length;
    const issuesCount = 0; // 暂时设为0

    document.getElementById('activeEventsCount').textContent = activeEventsCount;
    document.getElementById('upcomingEventsCount').textContent = upcomingEventsCount;
    document.getElementById('activeParticipants').textContent = activeParticipants;
    document.getElementById('issuesCount').textContent = issuesCount;
}

function loadTrackingTable() {
    const tableBody = document.getElementById('trackingTable');
    if (!tableBody) return;

    const trackingEvents = events.filter(e => e.status === 'active' || e.status === 'pending');

    if (trackingEvents.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无跟踪数据</td></tr>';
        return;
    }

    const html = trackingEvents.map(event => {
        const progress = Math.round((event.currentParticipants / event.maxParticipants) * 100);
        const daysLeft = Math.ceil((new Date(event.startDate) - new Date()) / (1000 * 60 * 60 * 24));

        return `
            <tr>
                <td>${event.name}</td>
                <td>${event.status === 'pending' ? '报名阶段' : '进行中'}</td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" style="width: ${progress}%">${progress}%</div>
                    </div>
                </td>
                <td>${event.currentParticipants}/${event.maxParticipants}</td>
                <td>${daysLeft > 0 ? daysLeft + '天' : '已开始'}</td>
                <td><span class="badge status-${event.status}">${getStatusText(event.status)}</span></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewEventDetails('${event.id}')" title="详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="updateEventStatus('${event.id}')" title="更新状态">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');

    tableBody.innerHTML = html;
}

function refreshTrackingData() {
    loadTrackingData();
    showNotification('跟踪数据已刷新', 'info');
}

function exportTrackingReport() {
    showNotification('导出功能开发中...', 'info');
}

function viewEventDetails(eventId) {
    const event = events.find(e => e.id === eventId);
    if (event) {
        alert(`赛事详情:\n名称: ${event.name}\n类型: ${event.type}\n状态: ${getStatusText(event.status)}\n开始时间: ${event.startDate}\n参赛人数: ${event.currentParticipants}/${event.maxParticipants}`);
    }
}

function updateEventStatus(eventId) {
    const event = events.find(e => e.id === eventId);
    if (event) {
        const newStatus = prompt('请输入新状态 (pending/active/completed/cancelled):', event.status);
        if (newStatus && ['pending', 'active', 'completed', 'cancelled'].includes(newStatus)) {
            event.status = newStatus;
            loadTrackingTable();
            updateDashboard();
            showNotification('状态已更新', 'success');
        }
    }
}

// 归档相关函数
function loadArchiveData() {
    updateArchiveStats();
    loadArchiveTable();
}

function updateArchiveStats() {
    const completedEvents = events.filter(e => e.status === 'completed');
    const completedEventsCount = completedEvents.length;
    const totalWinners = completedEvents.length * 3; // 假设每个赛事有3个获奖者
    const archivedDataSize = (completedEvents.length * 2.5).toFixed(1); // 假设每个赛事2.5MB

    document.getElementById('completedEventsCount').textContent = completedEventsCount;
    document.getElementById('totalWinners').textContent = totalWinners;
    document.getElementById('archivedDataSize').textContent = archivedDataSize + ' MB';
}

function loadArchiveTable() {
    const tableBody = document.getElementById('archiveTable');
    if (!tableBody) return;

    const archivedEvents = events.filter(e => e.status === 'completed');

    if (archivedEvents.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无归档数据</td></tr>';
        return;
    }

    const html = archivedEvents.map(event => `
        <tr>
            <td>${event.name}</td>
            <td>${event.type}</td>
            <td>${formatDate(event.startDate)} - ${formatDate(event.endDate)}</td>
            <td>${event.currentParticipants}</td>
            <td>3人</td>
            <td>${formatDate(event.endDate)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewArchiveDetails('${event.id}')" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-success" onclick="downloadArchive('${event.id}')" title="下载归档">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');

    tableBody.innerHTML = html;
}

function refreshArchiveData() {
    loadArchiveData();
    showNotification('归档数据已刷新', 'info');
}

function exportArchiveData() {
    showNotification('Excel导出功能开发中...', 'info');
}

function viewArchiveDetails(eventId) {
    const event = events.find(e => e.id === eventId);
    if (event) {
        alert(`归档赛事详情:\n名称: ${event.name}\n类型: ${event.type}\n举办时间: ${event.startDate} - ${event.endDate}\n参赛人数: ${event.currentParticipants}\n状态: 已完成`);
    }
}

function downloadArchive(eventId) {
    showNotification('下载功能开发中...', 'info');
}
