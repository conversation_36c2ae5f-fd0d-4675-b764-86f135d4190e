<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>赛事管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#"><i class="fas fa-trophy me-2"></i>赛事管理系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#dashboard">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#registration">赛事备案</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#enrollment">赛事报名</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#tracking">赛事跟踪</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#archive">赛事归档</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar">
                <div class="sidebar-menu">
                    <h6 class="sidebar-heading">功能菜单</h6>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#dashboard" data-section="dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#registration" data-section="registration">
                                <i class="fas fa-file-alt me-2"></i>赛事备案
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#enrollment" data-section="enrollment">
                                <i class="fas fa-user-plus me-2"></i>赛事报名
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#tracking" data-section="tracking">
                                <i class="fas fa-chart-line me-2"></i>赛事跟踪
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#archive" data-section="archive">
                                <i class="fas fa-archive me-2"></i>赛事归档
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <!-- 仪表盘 -->
                <div id="dashboard" class="content-section active">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-tachometer-alt me-2"></i>系统仪表盘</h2>
                        <div class="btn-group">
                            <button class="btn btn-outline-primary btn-sm">刷新数据</button>
                        </div>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card stat-card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="totalEvents">0</h4>
                                            <p class="mb-0">总赛事数</p>
                                        </div>
                                        <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="activeEvents">0</h4>
                                            <p class="mb-0">进行中赛事</p>
                                        </div>
                                        <i class="fas fa-play-circle fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="totalParticipants">0</h4>
                                            <p class="mb-0">总参赛人数</p>
                                        </div>
                                        <i class="fas fa-users fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="pendingEvents">0</h4>
                                            <p class="mb-0">待审核赛事</p>
                                        </div>
                                        <i class="fas fa-clock fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近活动 -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-list me-2"></i>最近赛事活动</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>赛事名称</th>
                                                    <th>状态</th>
                                                    <th>参赛人数</th>
                                                    <th>开始时间</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="recentEventsTable">
                                                <tr>
                                                    <td colspan="5" class="text-center text-muted">暂无数据</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-bell me-2"></i>系统通知</h5>
                                </div>
                                <div class="card-body">
                                    <div id="notifications">
                                        <div class="alert alert-info alert-sm">
                                            <i class="fas fa-info-circle me-2"></i>
                                            欢迎使用赛事管理系统！
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 赛事备案模块 -->
                <div id="registration" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-file-alt me-2"></i>赛事备案</h2>
                        <button class="btn btn-primary" onclick="showRegistrationForm()">
                            <i class="fas fa-plus me-2"></i>新建赛事
                        </button>
                    </div>

                    <!-- 赛事列表 -->
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h5 class="mb-0">赛事列表</h5>
                                </div>
                                <div class="col-auto">
                                    <div class="input-group input-group-sm">
                                        <input type="text" class="form-control" placeholder="搜索赛事..." id="searchEvents">
                                        <button class="btn btn-outline-secondary" type="button">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>赛事ID</th>
                                            <th>赛事名称</th>
                                            <th>赛事类型</th>
                                            <th>状态</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="eventsTable">
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">暂无赛事数据</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 赛事报名模块 -->
                <div id="enrollment" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-user-plus me-2"></i>赛事报名</h2>
                        <div class="btn-group">
                            <button class="btn btn-outline-primary" onclick="refreshEnrollmentData()">
                                <i class="fas fa-sync-alt me-2"></i>刷新数据
                            </button>
                        </div>
                    </div>

                    <!-- 可报名赛事列表 -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-calendar-check me-2"></i>可报名赛事</h5>
                                </div>
                                <div class="card-body">
                                    <div id="availableEvents" class="row">
                                        <!-- 动态加载可报名赛事 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-user-check me-2"></i>报名统计</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>总报名人数</span>
                                            <strong id="totalEnrollments">0</strong>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>待审核</span>
                                            <strong id="pendingEnrollments">0</strong>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>已确认</span>
                                            <strong id="confirmedEnrollments">0</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 报名管理表格 -->
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h5 class="mb-0">报名管理</h5>
                                </div>
                                <div class="col-auto">
                                    <div class="input-group input-group-sm">
                                        <select class="form-select" id="enrollmentEventFilter">
                                            <option value="">所有赛事</option>
                                        </select>
                                        <input type="text" class="form-control" placeholder="搜索参赛者..." id="searchParticipants">
                                        <button class="btn btn-outline-secondary" type="button">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>参赛者</th>
                                            <th>赛事名称</th>
                                            <th>联系方式</th>
                                            <th>报名时间</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="participantsTable">
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">暂无报名数据</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 赛事跟踪模块 -->
                <div id="tracking" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-chart-line me-2"></i>赛事跟踪</h2>
                        <div class="btn-group">
                            <button class="btn btn-outline-primary" onclick="refreshTrackingData()">
                                <i class="fas fa-sync-alt me-2"></i>刷新数据
                            </button>
                            <button class="btn btn-primary" onclick="exportTrackingReport()">
                                <i class="fas fa-download me-2"></i>导出报告
                            </button>
                        </div>
                    </div>

                    <!-- 跟踪概览 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-play-circle fa-2x text-success mb-2"></i>
                                    <h4 id="activeEventsCount">0</h4>
                                    <p class="text-muted mb-0">进行中赛事</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                    <h4 id="upcomingEventsCount">0</h4>
                                    <p class="text-muted mb-0">即将开始</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-users fa-2x text-info mb-2"></i>
                                    <h4 id="activeParticipants">0</h4>
                                    <p class="text-muted mb-0">活跃参赛者</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                                    <h4 id="issuesCount">0</h4>
                                    <p class="text-muted mb-0">待处理问题</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 赛事进度表格 -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-tasks me-2"></i>赛事进度跟踪</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>赛事名称</th>
                                            <th>当前阶段</th>
                                            <th>进度</th>
                                            <th>参赛人数</th>
                                            <th>剩余时间</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="trackingTable">
                                        <tr>
                                            <td colspan="7" class="text-center text-muted">暂无跟踪数据</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 赛事归档模块 -->
                <div id="archive" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-archive me-2"></i>赛事归档</h2>
                        <div class="btn-group">
                            <button class="btn btn-outline-primary" onclick="refreshArchiveData()">
                                <i class="fas fa-sync-alt me-2"></i>刷新数据
                            </button>
                            <button class="btn btn-success" onclick="exportArchiveData()">
                                <i class="fas fa-file-excel me-2"></i>导出Excel
                            </button>
                        </div>
                    </div>

                    <!-- 归档统计 -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                    <h4 id="completedEventsCount">0</h4>
                                    <p class="text-muted mb-0">已完成赛事</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-medal fa-2x text-warning mb-2"></i>
                                    <h4 id="totalWinners">0</h4>
                                    <p class="text-muted mb-0">获奖者总数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-database fa-2x text-info mb-2"></i>
                                    <h4 id="archivedDataSize">0 MB</h4>
                                    <p class="text-muted mb-0">归档数据大小</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 归档赛事列表 -->
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h5 class="mb-0">归档赛事列表</h5>
                                </div>
                                <div class="col-auto">
                                    <div class="input-group input-group-sm">
                                        <select class="form-select" id="archiveYearFilter">
                                            <option value="">所有年份</option>
                                            <option value="2024">2024年</option>
                                            <option value="2023">2023年</option>
                                        </select>
                                        <input type="text" class="form-control" placeholder="搜索归档赛事..." id="searchArchive">
                                        <button class="btn btn-outline-secondary" type="button">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>赛事名称</th>
                                            <th>赛事类型</th>
                                            <th>举办时间</th>
                                            <th>参赛人数</th>
                                            <th>获奖者</th>
                                            <th>归档时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="archiveTable">
                                        <tr>
                                            <td colspan="7" class="text-center text-muted">暂无归档数据</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <!-- 新建赛事模态框 -->
    <div class="modal fade" id="newEventModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>新建赛事</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="newEventForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="eventName" class="form-label">赛事名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="eventName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="eventType" class="form-label">赛事类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="eventType" required>
                                        <option value="">请选择赛事类型</option>
                                        <option value="体育竞技">体育竞技</option>
                                        <option value="技能竞赛">技能竞赛</option>
                                        <option value="艺术创作">艺术创作</option>
                                        <option value="学术竞赛">学术竞赛</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="startDate" class="form-label">开始时间 <span class="text-danger">*</span></label>
                                    <input type="datetime-local" class="form-control" id="startDate" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="endDate" class="form-label">结束时间 <span class="text-danger">*</span></label>
                                    <input type="datetime-local" class="form-control" id="endDate" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="maxParticipants" class="form-label">最大参赛人数 <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="maxParticipants" min="1" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="registrationDeadline" class="form-label">报名截止时间</label>
                                    <input type="datetime-local" class="form-control" id="registrationDeadline">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="eventDescription" class="form-label">赛事描述</label>
                            <textarea class="form-control" id="eventDescription" rows="3" placeholder="请输入赛事详细描述..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="eventLocation" class="form-label">举办地点</label>
                            <input type="text" class="form-control" id="eventLocation" placeholder="请输入举办地点">
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contactPerson" class="form-label">联系人</label>
                                    <input type="text" class="form-control" id="contactPerson">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contactPhone" class="form-label">联系电话</label>
                                    <input type="tel" class="form-control" id="contactPhone">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveNewEvent()">保存赛事</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 参赛者报名模态框 -->
    <div class="modal fade" id="enrollmentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-user-plus me-2"></i>参赛报名</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="enrollmentForm">
                        <input type="hidden" id="enrollEventId">
                        <div class="mb-3">
                            <label for="participantName" class="form-label">姓名 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="participantName" required>
                        </div>
                        <div class="mb-3">
                            <label for="participantEmail" class="form-label">邮箱 <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="participantEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="participantPhone" class="form-label">手机号 <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="participantPhone" required>
                        </div>
                        <div class="mb-3">
                            <label for="participantAge" class="form-label">年龄</label>
                            <input type="number" class="form-control" id="participantAge" min="1" max="120">
                        </div>
                        <div class="mb-3">
                            <label for="participantGender" class="form-label">性别</label>
                            <select class="form-select" id="participantGender">
                                <option value="">请选择</option>
                                <option value="male">男</option>
                                <option value="female">女</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="participantNotes" class="form-label">备注</label>
                            <textarea class="form-control" id="participantNotes" rows="2" placeholder="其他需要说明的信息..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveEnrollment()">提交报名</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script src="demo-data.js"></script>
</body>
</html>
