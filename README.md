# 赛事管理系统

一个完整的赛事管理系统，覆盖从赛事备案到归档的全流程管理。

## 功能特性

### 🏆 核心功能模块

1. **赛事备案**
   - 赛事基本信息录入
   - 赛事类型选择（体育竞技、技能竞赛、艺术创作等）
   - 时间安排和地点设置
   - 参赛人数限制
   - 联系人信息管理

2. **赛事报名**
   - 在线报名表单
   - 参赛者信息收集
   - 报名状态管理（待审核、已确认、已拒绝）
   - 报名人数统计
   - 报名截止时间控制

3. **赛事跟踪**
   - 实时赛事状态监控
   - 参赛者进度跟踪
   - 赛事进度可视化
   - 问题和异常处理
   - 数据统计分析

4. **赛事归档**
   - 已完成赛事存档
   - 获奖者信息记录
   - 历史数据查询
   - 数据导出功能
   - 统计报告生成

### 🎨 界面特性

- **现代化UI设计**：基于Bootstrap 5的响应式设计
- **直观的导航**：侧边栏导航，快速切换功能模块
- **数据可视化**：统计卡片、进度条、图表展示
- **移动端适配**：完全响应式设计，支持各种设备
- **交互友好**：模态框、通知提醒、加载动画

### 📊 数据管理

- **实时统计**：总赛事数、进行中赛事、参赛人数等
- **状态管理**：待审核、进行中、已完成、已取消
- **搜索过滤**：支持按名称、类型、状态等条件筛选
- **批量操作**：批量审核、状态更新等

## 技术栈

- **前端框架**：HTML5 + CSS3 + JavaScript (ES6+)
- **UI框架**：Bootstrap 5.3.0
- **图标库**：Font Awesome 6.0.0
- **数据存储**：本地JavaScript对象（可扩展为后端数据库）

## 文件结构

```
赛事管理系统/
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # JavaScript逻辑文件
└── README.md           # 项目说明文档
```

## 快速开始

1. **下载项目文件**
   ```bash
   # 确保所有文件在同一目录下
   - index.html
   - styles.css  
   - script.js
   ```

2. **打开系统**
   - 直接在浏览器中打开 `index.html` 文件
   - 或使用本地服务器（推荐）

3. **开始使用**
   - 系统会自动加载示例数据
   - 通过侧边栏导航切换不同功能模块
   - 点击"新建赛事"开始创建第一个赛事

## 使用指南

### 创建赛事
1. 点击"赛事备案"模块
2. 点击"新建赛事"按钮
3. 填写赛事基本信息
4. 设置时间和参赛人数限制
5. 保存赛事

### 管理报名
1. 进入"赛事报名"模块
2. 查看可报名赛事列表
3. 点击"立即报名"填写参赛者信息
4. 在报名管理表格中审核参赛者

### 跟踪进度
1. 访问"赛事跟踪"模块
2. 查看实时统计数据
3. 监控各赛事进度
4. 更新赛事状态

### 归档管理
1. 进入"赛事归档"模块
2. 查看已完成赛事
3. 导出历史数据
4. 生成统计报告

## 功能演示

### 仪表盘
- 显示系统总体统计信息
- 最近赛事活动列表
- 系统通知和提醒

### 赛事备案
- 创建新赛事表单
- 赛事列表管理
- 搜索和筛选功能

### 赛事报名
- 可报名赛事展示
- 在线报名表单
- 参赛者管理界面

### 赛事跟踪
- 进度可视化展示
- 实时状态监控
- 数据统计分析

### 赛事归档
- 历史赛事查询
- 数据导出功能
- 统计报告生成

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 扩展功能

系统设计为可扩展架构，可以轻松添加以下功能：

- **用户权限管理**：管理员、组织者、参赛者角色
- **支付集成**：报名费用收取
- **邮件通知**：自动发送确认邮件
- **文件上传**：参赛作品、证明文件上传
- **评分系统**：在线评分和排名
- **数据库集成**：MySQL、PostgreSQL等
- **API接口**：RESTful API支持
- **移动应用**：React Native、Flutter等

## 许可证

MIT License - 可自由使用、修改和分发

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱：<EMAIL>
- 电话：400-123-4567

---

**赛事管理系统** - 让赛事组织更简单、更高效！
