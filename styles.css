/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 侧边栏样式 */
.sidebar {
    background-color: #fff;
    min-height: calc(100vh - 56px);
    border-right: 1px solid #dee2e6;
    padding: 0;
}

.sidebar-menu {
    padding: 1rem;
}

.sidebar-heading {
    color: #6c757d;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 1rem;
    padding-left: 0.5rem;
}

.sidebar .nav-link {
    color: #495057;
    padding: 0.75rem 0.5rem;
    border-radius: 0.375rem;
    margin-bottom: 0.25rem;
    transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
    background-color: #e9ecef;
    color: #0d6efd;
}

.sidebar .nav-link.active {
    background-color: #0d6efd;
    color: white;
}

.sidebar .nav-link i {
    width: 20px;
}

/* 主内容区样式 */
.main-content {
    padding: 2rem;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* 统计卡片样式 */
.stat-card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.stat-card .card-body {
    padding: 1.5rem;
}

.stat-card h4 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-card p {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.75rem 0.75rem 0 0 !important;
    padding: 1rem 1.5rem;
}

.card-header h5 {
    margin-bottom: 0;
    color: #495057;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* 按钮样式 */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
    transform: translateY(-1px);
}

/* 状态标签样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-active {
    background-color: #198754;
    color: #fff;
}

.status-completed {
    background-color: #6c757d;
    color: #fff;
}

.status-cancelled {
    background-color: #dc3545;
    color: #fff;
}

/* 通知样式 */
.alert-sm {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.5rem;
}

.alert-sm:last-child {
    margin-bottom: 0;
}

/* 表单样式 */
.form-control {
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    padding: 0.75rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* 输入组样式 */
.input-group-sm .form-control {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.input-group-sm .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 56px;
        left: -250px;
        width: 250px;
        height: calc(100vh - 56px);
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .stat-card h4 {
        font-size: 1.5rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content-section.active {
    animation: fadeIn 0.3s ease;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模态框样式 */
.modal-content {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 进度条样式 */
.progress {
    background-color: #e9ecef;
    border-radius: 0.5rem;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    transition: width 0.6s ease;
}

/* 赛事卡片样式 */
.event-card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-color: #0d6efd;
}

.event-card .card-title {
    color: #495057;
    font-weight: 600;
}

.event-card .card-text {
    font-size: 0.875rem;
    line-height: 1.4;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.pending {
    background-color: #ffc107;
}

.status-indicator.active {
    background-color: #198754;
}

.status-indicator.completed {
    background-color: #6c757d;
}

.status-indicator.cancelled {
    background-color: #dc3545;
}

/* 数据表格增强样式 */
.table-enhanced {
    font-size: 0.9rem;
}

.table-enhanced th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table-enhanced td {
    border-bottom: 1px solid #f1f3f4;
}

.table-enhanced tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.08);
}

/* 搜索和过滤器样式 */
.filter-section {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.filter-section .form-control,
.filter-section .form-select {
    border: 1px solid #ced4da;
    background-color: white;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h5 {
    margin-bottom: 0.5rem;
    color: #495057;
}

.empty-state p {
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

/* 操作按钮组样式 */
.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.action-buttons .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
}

/* 统计卡片动画 */
.stat-card {
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.stat-card:hover::before {
    left: 100%;
}

/* 表单增强样式 */
.form-floating {
    position: relative;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* 模态框增强 */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    background: linear-gradient(135deg, #0d6efd, #6610f2);
    color: white;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: #495057;
    border-radius: 0.375rem;
}

/* 加载状态 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 0.3rem solid #f3f3f3;
    border-top: 0.3rem solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 响应式增强 */
@media (max-width: 576px) {
    .main-content {
        padding: 1rem 0.5rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn-group-sm .btn {
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
    }

    .table-responsive {
        font-size: 0.8rem;
    }

    .modal-dialog {
        margin: 0.5rem;
    }
}

@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    .btn,
    .modal {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}
