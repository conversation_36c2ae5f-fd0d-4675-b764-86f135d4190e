// 演示数据文件 - 可选加载更多示例数据

// 扩展的赛事数据
const extendedEvents = [
    {
        id: 'EVT004',
        name: '2024年夏季游泳锦标赛',
        type: '体育竞技',
        status: 'active',
        startDate: '2024-06-15',
        endDate: '2024-06-17',
        maxParticipants: 300,
        currentParticipants: 245,
        createTime: '2024-04-01',
        description: '全国性游泳锦标赛，包含自由泳、蛙泳、蝶泳、仰泳等项目',
        location: '国家游泳中心',
        contactPerson: '王教练',
        contactPhone: '13900139001',
        registrationDeadline: '2024-06-01T23:59'
    },
    {
        id: 'EVT005',
        name: '青少年机器人编程大赛',
        type: '技能竞赛',
        status: 'pending',
        startDate: '2024-07-20',
        endDate: '2024-07-22',
        maxParticipants: 150,
        currentParticipants: 67,
        createTime: '2024-04-15',
        description: '面向12-18岁青少年的机器人编程竞赛',
        location: '科技馆',
        contactPerson: '李老师',
        contactPhone: '13800138003',
        registrationDeadline: '2024-07-10T18:00'
    },
    {
        id: 'EVT006',
        name: '城市马拉松接力赛',
        type: '体育竞技',
        status: 'completed',
        startDate: '2024-03-15',
        endDate: '2024-03-15',
        maxParticipants: 400,
        currentParticipants: 384,
        createTime: '2024-01-20',
        description: '4人团队接力马拉松，每人跑10.5公里',
        location: '城市中央公园',
        contactPerson: '张主任',
        contactPhone: '13700137001',
        registrationDeadline: '2024-03-01T23:59'
    },
    {
        id: 'EVT007',
        name: '大学生创新创业大赛',
        type: '学术竞赛',
        status: 'active',
        startDate: '2024-05-01',
        endDate: '2024-05-31',
        maxParticipants: 200,
        currentParticipants: 156,
        createTime: '2024-03-01',
        description: '面向在校大学生的创新创业项目竞赛',
        location: '大学城会议中心',
        contactPerson: '陈教授',
        contactPhone: '13600136001',
        registrationDeadline: '2024-04-20T23:59'
    },
    {
        id: 'EVT008',
        name: '少儿绘画比赛',
        type: '艺术创作',
        status: 'pending',
        startDate: '2024-06-01',
        endDate: '2024-06-01',
        maxParticipants: 100,
        currentParticipants: 43,
        createTime: '2024-04-10',
        description: '6-12岁儿童绘画创作比赛，主题：我的梦想',
        location: '文化艺术中心',
        contactPerson: '刘老师',
        contactPhone: '13500135001',
        registrationDeadline: '2024-05-25T17:00'
    }
];

// 扩展的参赛者数据
const extendedParticipants = [
    {
        id: 'P003',
        name: '王小明',
        email: '<EMAIL>',
        phone: '13800138003',
        age: 25,
        gender: 'male',
        eventId: 'EVT004',
        registrationTime: '2024-04-05',
        status: 'confirmed',
        notes: '专业游泳运动员'
    },
    {
        id: 'P004',
        name: '李小红',
        email: '<EMAIL>',
        phone: '13800138004',
        age: 16,
        gender: 'female',
        eventId: 'EVT005',
        registrationTime: '2024-04-16',
        status: 'pending',
        notes: '有Python编程基础'
    },
    {
        id: 'P005',
        name: '张大伟',
        email: '<EMAIL>',
        phone: '13800138005',
        age: 30,
        gender: 'male',
        eventId: 'EVT006',
        registrationTime: '2024-02-15',
        status: 'confirmed',
        notes: '马拉松爱好者，PB 3:30'
    },
    {
        id: 'P006',
        name: '陈美丽',
        email: '<EMAIL>',
        phone: '13800138006',
        age: 22,
        gender: 'female',
        eventId: 'EVT007',
        registrationTime: '2024-03-10',
        status: 'confirmed',
        notes: '计算机专业大四学生'
    },
    {
        id: 'P007',
        name: '刘小天',
        email: '<EMAIL>',
        phone: '13800138007',
        age: 8,
        gender: 'male',
        eventId: 'EVT008',
        registrationTime: '2024-04-12',
        status: 'pending',
        notes: '喜欢画动物'
    },
    {
        id: 'P008',
        name: '赵小花',
        email: '<EMAIL>',
        phone: '13800138008',
        age: 28,
        gender: 'female',
        eventId: 'EVT001',
        registrationTime: '2024-03-08',
        status: 'confirmed',
        notes: '业余跑步爱好者'
    },
    {
        id: 'P009',
        name: '孙小强',
        email: '<EMAIL>',
        phone: '13800138009',
        age: 19,
        gender: 'male',
        eventId: 'EVT002',
        registrationTime: '2024-03-20',
        status: 'confirmed',
        notes: 'ACM竞赛经验丰富'
    },
    {
        id: 'P010',
        name: '周小雨',
        email: '<EMAIL>',
        phone: '13800138010',
        age: 24,
        gender: 'female',
        eventId: 'EVT004',
        registrationTime: '2024-04-08',
        status: 'pending',
        notes: '游泳教练'
    }
];

// 加载扩展数据的函数
function loadExtendedDemoData() {
    // 合并赛事数据
    events.push(...extendedEvents);
    
    // 合并参赛者数据
    participants.push(...extendedParticipants);
    
    // 更新赛事的参赛人数
    events.forEach(event => {
        const eventParticipants = participants.filter(p => p.eventId === event.id);
        event.currentParticipants = eventParticipants.length;
    });
    
    // 更新界面
    updateDashboard();
    loadEventsTable();
    
    showNotification('演示数据已加载！', 'success');
}

// 重置数据的函数
function resetDemoData() {
    // 重置为初始数据
    events.length = 0;
    participants.length = 0;
    
    // 重新加载初始示例数据
    loadSampleData();
    
    // 更新界面
    updateDashboard();
    loadEventsTable();
    
    showNotification('数据已重置！', 'info');
}

// 导出数据的函数
function exportDemoData() {
    const data = {
        events: events,
        participants: participants,
        exportTime: new Date().toISOString(),
        version: '1.0'
    };
    
    const dataStr = JSON.stringify(data, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `赛事数据_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    showNotification('数据导出成功！', 'success');
}

// 生成统计报告
function generateStatisticsReport() {
    const report = {
        总体统计: {
            赛事总数: events.length,
            参赛者总数: participants.length,
            进行中赛事: events.filter(e => e.status === 'active').length,
            已完成赛事: events.filter(e => e.status === 'completed').length,
            待审核赛事: events.filter(e => e.status === 'pending').length
        },
        赛事类型分布: {},
        参赛者状态分布: {
            已确认: participants.filter(p => p.status === 'confirmed').length,
            待审核: participants.filter(p => p.status === 'pending').length,
            已拒绝: participants.filter(p => p.status === 'rejected').length
        },
        月度统计: {}
    };
    
    // 统计赛事类型分布
    events.forEach(event => {
        report.赛事类型分布[event.type] = (report.赛事类型分布[event.type] || 0) + 1;
    });
    
    // 统计月度数据
    events.forEach(event => {
        const month = event.createTime.substring(0, 7); // YYYY-MM
        report.月度统计[month] = (report.月度统计[month] || 0) + 1;
    });
    
    console.log('统计报告：', report);
    alert('统计报告已生成，请查看浏览器控制台！');
    
    return report;
}

// 在页面加载完成后添加演示数据控制按钮
document.addEventListener('DOMContentLoaded', function() {
    // 延迟添加演示控制按钮，确保主要功能已加载
    setTimeout(addDemoControls, 1000);
});

function addDemoControls() {
    const dashboardSection = document.getElementById('dashboard');
    if (dashboardSection) {
        const controlsHtml = `
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-cogs me-2"></i>演示数据控制</h5>
                </div>
                <div class="card-body">
                    <div class="btn-group me-2">
                        <button class="btn btn-outline-primary" onclick="loadExtendedDemoData()">
                            <i class="fas fa-plus me-1"></i>加载更多数据
                        </button>
                        <button class="btn btn-outline-secondary" onclick="resetDemoData()">
                            <i class="fas fa-refresh me-1"></i>重置数据
                        </button>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-outline-success" onclick="exportDemoData()">
                            <i class="fas fa-download me-1"></i>导出数据
                        </button>
                        <button class="btn btn-outline-info" onclick="generateStatisticsReport()">
                            <i class="fas fa-chart-bar me-1"></i>生成报告
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        dashboardSection.insertAdjacentHTML('beforeend', controlsHtml);
    }
}
